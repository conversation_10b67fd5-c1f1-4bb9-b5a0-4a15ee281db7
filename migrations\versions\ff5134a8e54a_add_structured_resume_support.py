"""add_structured_resume_support

Revision ID: ff5134a8e54a
Revises: ad867a6b38d9
Create Date: 2025-08-23 18:56:57.085256

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'ff5134a8e54a'
down_revision: Union[str, None] = 'ad867a6b38d9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('resumes', sa.Column('structured_data', sa.JSON(), nullable=True))

    # Add resume_type column with default value first
    op.add_column('resumes', sa.Column('resume_type', sa.String(), nullable=True))

    # Update existing records to have 'uploaded' as resume_type
    op.execute("UPDATE resumes SET resume_type = 'uploaded' WHERE resume_type IS NULL")

    # Now make the column NOT NULL
    op.alter_column('resumes', 'resume_type', nullable=False)

    op.alter_column('resumes', 'file_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('resumes', 'file_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('resumes', 'resume_type')
    op.drop_column('resumes', 'structured_data')
    # ### end Alembic commands ###
