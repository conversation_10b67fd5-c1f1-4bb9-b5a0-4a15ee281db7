# src/job_app/services/pdf_generator.py

import logging
from io import BytesIO
from xhtml2pdf import pisa


import markdown  # <-- Import the new library


logger = logging.getLogger(__name__)
def create_pdf_from_text(text_content: str) -> bytes:
    """
    Debug version with comprehensive error checking and simplified CSS
    """
    result_file = BytesIO()
    
    try:
        
        
        # Start with basic extensions only
        html_content = markdown.markdown(
            text_content, 
            extensions=['fenced_code', 'tables']  # Removed problematic extensions
        )
        
   
        # Simplified CSS that's more compatible with xhtml2pdf
        source_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{
                    size: a4 portrait;
                    margin: 2cm;
                }}
                
                body {{
                    font-family: Arial, sans-serif;
                    font-size: 11pt;
                    line-height: 1.4;
                    color: #333;
                }}
                
                h1 {{
                    font-size: 20pt;
                    color: #2c3e50;
                    margin-bottom: 0.5em;
                    page-break-after: avoid;
                }}
                
                h2 {{
                    font-size: 16pt;
                    color: #34495e;
                    margin-top: 1em;
                    margin-bottom: 0.5em;
                    page-break-after: avoid;
                }}
                
                h3 {{
                    font-size: 14pt;
                    color: #34495e;
                    margin-top: 0.8em;
                    margin-bottom: 0.4em;
                }}
                
                p {{
                    margin-bottom: 0.8em;
                }}
                
                ul, ol {{
                    margin-bottom: 0.8em;
                    padding-left: 20px;
                }}
                
                li {{
                    margin-bottom: 0.3em;
                }}
                
                strong {{
                    font-weight: bold;
                }}
                
                em {{
                    font-style: italic;
                }}
                
                pre {{
                    background-color: #f5f5f5;
                    padding: 10px;
                    border: 1px solid #ddd;
                    white-space: pre-wrap;
                    font-family: Courier, monospace;
                    font-size: 9pt;
                }}
                
                code {{
                    font-family: Courier, monospace;
                    background-color: #f0f0f0;
                    padding: 2px 4px;
                }}
                
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 1em;
                }}
                
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                
                th {{
                    background-color: #f0f0f0;
                    font-weight: bold;
                }}
                
                blockquote {{
                    border-left: 4px solid #ddd;
                    margin: 1em 0;
                    padding-left: 15px;
                    color: #666;
                    font-style: italic;
                }}
                
                hr {{
                    border: none;
                    height: 1px;
                    background-color: #ddd;
                    margin: 1.5em 0;
                }}
            </style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """
        
    
        
        # Generate PDF with error checking
        pisa_status = pisa.CreatePDF(
            src=source_html,
            dest=result_file,
            encoding='UTF-8'
        )
        
        
        if pisa_status.err:
            logger.error(f"PDF generation failed with errors: {pisa_status.err}")
            print(f"ERRORS: {pisa_status.err}")
            return b""
        
        if pisa_status.warn:
            logger.warning(f"PDF generation completed with warnings: {pisa_status.warn}")
            print(f"WARNINGS: {pisa_status.warn}")
        
        pdf_bytes = result_file.getvalue()
        print(f"Step 4: PDF generated successfully, size: {len(pdf_bytes)} bytes")
        
        if len(pdf_bytes) == 0:
            print("ERROR: PDF is empty!")
            return b""
        
        # Validate PDF header
        if not pdf_bytes.startswith(b'%PDF-'):
            print("ERROR: Generated file doesn't have PDF header!")
            return b""
        
        logger.info(f"Successfully generated PDF of size {len(pdf_bytes)} bytes.")
        return pdf_bytes

    except Exception as e:
        logger.error(f"Exception during PDF generation: {e}", exc_info=True)
        print(f"EXCEPTION: {e}")
        import traceback
        traceback.print_exc()
        return b""
    finally:
        result_file.close()