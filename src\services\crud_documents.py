# src/job_app/services/crud_documents.py

from sqlalchemy.orm import Session, joinedload
from typing import List, Optional

from src.db.models import User, Resume, JobDescription, GeneratedDocument
from src.schemas.job_description import JobDescriptionCreate, JobDescriptionUpdate
from src.schemas.resume import ResumeUpdate
from src.schemas.structured_resume import StructuredResumeCreate
from src.schemas.generated_document import GeneratedDocumentUpdateMetadata
from src.services.pdf_generator import create_pdf_from_text
from src.services.resume_formatter import convert_structured_resume_to_text
from src.storage.db_binary import upload_file_to_db

# --- Reusable Getters with Permission Checks ---

def get_resume_by_id(db: Session, resume_id: int, user: User) -> Optional[Resume]:
    """Fetches a resume by its ID, ensuring it belongs to the specified user."""
    return db.query(Resume).filter(
        Resume.id == resume_id,
        Resume.owner_id == user.id
    ).first()

def get_job_description_by_id(db: Session, jd_id: int, user: User) -> Optional[JobDescription]:
    """Fetches a job description by its ID, ensuring it belongs to the user."""
    return db.query(JobDescription).filter(
        JobDescription.id == jd_id,
        JobDescription.owner_id == user.id
    ).first()

def get_generated_document_by_id(db: Session, doc_id: int, user: User) -> Optional[GeneratedDocument]:
    """Fetches a generated document by its ID, ensuring it belongs to the user."""
    # Eagerly load the associated file to prevent extra DB queries later
    return db.query(GeneratedDocument).options(
        joinedload(GeneratedDocument.file)
    ).filter(
        GeneratedDocument.id == doc_id,
        GeneratedDocument.owner_id == user.id
    ).first()

# --- List Functions ---

def get_all_resumes_for_user(db: Session, user: User) -> List[Resume]:
    """Fetches all resumes for a given user."""
    return db.query(Resume).filter(Resume.owner_id == user.id).all()

def get_all_job_descriptions_for_user(db: Session, user: User) -> List[JobDescription]:
    """Fetches all job descriptions for a given user."""
    return db.query(JobDescription).filter(JobDescription.owner_id == user.id).all()

def get_all_generated_documents_for_user(db: Session, user: User) -> List[GeneratedDocument]:
    """Fetches all generated documents for a given user."""
    return db.query(GeneratedDocument).filter(GeneratedDocument.owner_id == user.id).order_by(GeneratedDocument.created_at.desc()).all()


# --- Creation Functions ---

def create_resume_for_user(db: Session, user: User, file_record) -> Resume:
    """Creates a new Resume record linked to a user and a file record."""
    db_resume = Resume(owner=user, file=file_record, resume_type="uploaded")
    db.add(db_resume)
    db.commit()
    db.refresh(db_resume)
    return db_resume

def create_structured_resume_for_user(db: Session, user: User, resume_data: StructuredResumeCreate) -> Resume:
    """Creates a new Resume record from structured data."""
    try:
        # Convert structured data to formatted text
        formatted_text = convert_structured_resume_to_text(resume_data)

        # Generate PDF from the formatted text
        pdf_bytes = create_pdf_from_text(formatted_text)

        if not pdf_bytes:
            raise ValueError("Failed to generate PDF from resume data")

        # Create file record for the generated PDF
        pdf_filename = f"structured_resume_{user.id}.pdf"
        file_record = upload_file_to_db(
            db=db,
            file_content=pdf_bytes,
            filename=pdf_filename,
            content_type="application/pdf",
            uploader=user
        )

        # Convert structured data to JSON-serializable format
        # This handles date objects and other non-JSON serializable types
        structured_data_dict = resume_data.model_dump(mode='json')

        # Create resume record with structured data
        db_resume = Resume(
            owner=user,
            file=file_record,
            resume_type="structured",
            extracted_text=formatted_text,
            structured_data=structured_data_dict
        )

        db.add(db_resume)
        db.commit()
        db.refresh(db_resume)
        return db_resume

    except Exception as e:
        db.rollback()
        raise ValueError(f"Failed to create structured resume: {str(e)}")

def create_job_description_for_user(db: Session, user: User, jd_create: JobDescriptionCreate) -> JobDescription:
    """Creates a new JobDescription record for a user."""
    db_jd = JobDescription(
        owner=user,
        title=jd_create.title,
        company=jd_create.company,
        description_text=jd_create.description_text
    )
    db.add(db_jd)
    db.commit()
    db.refresh(db_jd)
    return db_jd

def create_generated_document_for_task(
    db: Session,
    user: User,
    doc_type: str,
    resume: Resume,
    job_description: Optional[JobDescription] = None
) -> GeneratedDocument:
    """Creates the initial GeneratedDocument record with a 'pending' status."""
    db_generated_doc = GeneratedDocument(
        owner=user,
        type=doc_type,
        source_resume=resume,
        source_job_description=job_description,
        status="pending"
    )
    db.add(db_generated_doc)
    db.commit()
    db.refresh(db_generated_doc)
    return db_generated_doc

def update_generated_document_content(
    db: Session,
    doc_id: int,
    user: User,
    new_content: str
) -> Optional[GeneratedDocument]:
    """Updates a generated document's content and regenerates its PDF.
    
    Args:
        db: Database session
        doc_id: ID of the document to update
        user: User making the update
        new_content: New text content for the document
        
    Returns:
        Updated GeneratedDocument if successful, None if document not found
        
    Raises:
        ValueError: If the document is not in a state that can be updated
    """
    doc = get_generated_document_by_id(db, doc_id, user)
    if not doc:
        return None
        
    # Only allow updates to completed documents
    if doc.status != "completed":
        raise ValueError("Can only update completed documents")
        
    try:
        # Update the text content
        doc.content = new_content
        
        # Generate new PDF
        pdf_bytes = create_pdf_from_text(new_content)
        if pdf_bytes:
            # Create a new filename for the updated PDF
            pdf_filename = f"{doc.type}_{doc.id}_{user.id}_updated.pdf"
            
            # Create new file record first
            db_file_record = upload_file_to_db(
                db=db,
                file_content=pdf_bytes,
                filename=pdf_filename,
                content_type="application/pdf",
                uploader=user
            )
            
            # Update the document's file relationship
            old_file = doc.file
            doc.file = db_file_record
            
            # Now we can safely delete the old file
            if old_file:
                db.delete(old_file)
            
        db.commit()
        db.refresh(doc)
        return doc
        
    except Exception as e:
        db.rollback()
        raise ValueError(f"Failed to update document: {str(e)}")

def update_generated_document(db: Session, doc_id: int, user: User, update_data: GeneratedDocumentUpdateMetadata) -> Optional[GeneratedDocument]:
    """Updates a generated document's metadata."""
    doc = get_generated_document_by_id(db, doc_id, user)
    if not doc:
        return None
    
    if update_data.type:
        doc.type = update_data.type
    if update_data.status:
        doc.status = update_data.status
        
    db.commit()
    db.refresh(doc)
    return doc

def delete_generated_document(db: Session, doc_id: int, user: User) -> bool:
    """Deletes a generated document."""
    doc = get_generated_document_by_id(db, doc_id, user)
    if not doc:
        return False
    
    db.delete(doc)
    db.commit()
    return True

def update_job_description(db: Session, jd_id: int, user: User, update_data: JobDescriptionUpdate) -> Optional[JobDescription]:
    """Updates a job description."""
    jd = get_job_description_by_id(db, jd_id, user)
    if not jd:
        return None
    
    if update_data.title:
        jd.title = update_data.title
    if update_data.company:
        jd.company = update_data.company
    if update_data.description_text:
        jd.description_text = update_data.description_text
        
    db.commit()
    db.refresh(jd)
    return jd

def delete_job_description(db: Session, jd_id: int, user: User) -> bool:
    """Deletes a job description."""
    jd = get_job_description_by_id(db, jd_id, user)
    if not jd:
        return False
    
    db.delete(jd)
    db.commit()
    return True

def update_resume(db: Session, resume_id: int, user: User, update_data: ResumeUpdate) -> Optional[Resume]:
    """Updates a resume."""
    resume = get_resume_by_id(db, resume_id, user)
    if not resume:
        return None
    
    if update_data.extracted_text:
        resume.extracted_text = update_data.extracted_text
        
    db.commit()
    db.refresh(resume)
    return resume

def delete_resume(db: Session, resume_id: int, user: User) -> bool:
    """Deletes a resume."""
    resume = get_resume_by_id(db, resume_id, user)
    if not resume:
        return False
    
    db.delete(resume)
    db.commit()
    return True