"""Add file_records table and link to resumes and generated docs

Revision ID: a9eb942fe0f3
Revises: 7fa060ce0039
Create Date: 2025-06-07 05:13:05.503559

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a9eb942fe0f3'
down_revision: Union[str, None] = '7fa060ce0039'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('file_records',
    sa.Column('id', sa.Integer(), nullable=True),
    sa.Column('filename', sa.String(length=255), nullable=True),
    sa.Column('content_type', sa.String(length=100), nullable=True),
    sa.Column('size', sa.Integer(), nullable=True),
    sa.Column('content', sa.LargeBinary(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('metadata', sa.JSON(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_file_records_id'), 'file_records', ['id'], unique=True)
    op.add_column('generated_documents', sa.Column('file_id', sa.Integer(), nullable=True))
    op.create_unique_constraint(None, 'generated_documents', ['file_id'])
    op.create_foreign_key(None, 'generated_documents', 'file_records', ['file_id'], ['id'])
    op.drop_column('generated_documents', 'storage_path')
    op.add_column('resumes', sa.Column('file_id', sa.Integer(), nullable=True))
    op.create_unique_constraint(None, 'resumes', ['file_id'])
    op.create_foreign_key(None, 'resumes', 'file_records', ['file_id'], ['id'])
    op.drop_column('resumes', 'storage_path')
    op.drop_column('resumes', 'original_filename')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('resumes', sa.Column('original_filename', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('resumes', sa.Column('storage_path', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'resumes', type_='foreignkey')
    op.drop_constraint(None, 'resumes', type_='unique')
    op.drop_column('resumes', 'file_id')
    op.add_column('generated_documents', sa.Column('storage_path', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'generated_documents', type_='foreignkey')
    op.drop_constraint(None, 'generated_documents', type_='unique')
    op.drop_column('generated_documents', 'file_id')
    op.drop_index(op.f('ix_file_records_id'), table_name='file_records')
    op.drop_table('file_records')
    # ### end Alembic commands ###
