# src/services/resume_formatter.py

from datetime import date
from typing import List, Optional
from src.schemas.structured_resume import (
    StructuredResumeCreate, ContactInfo, WorkExperience, 
    Education, SkillCategory, Project, Certification
)

def format_date_range(start_date: Optional[date], end_date: Optional[date], is_current: bool = False) -> str:
    """Format date range for resume sections"""
    if not start_date:
        return ""
    
    start_str = start_date.strftime("%B %Y")
    
    if is_current:
        return f"{start_str} - Present"
    elif end_date:
        end_str = end_date.strftime("%B %Y")
        return f"{start_str} - {end_str}"
    else:
        return start_str

def format_contact_info(contact: ContactInfo) -> str:
    """Format contact information section"""
    lines = []
    
    # Name as header
    full_name = f"{contact.first_name} {contact.last_name}"
    lines.append(f"# {full_name}")
    lines.append("")
    
    # Contact details
    contact_details = []
    contact_details.append(contact.email)
    
    if contact.phone:
        contact_details.append(contact.phone)
    if contact.location:
        contact_details.append(contact.location)
    if contact.linkedin:
        contact_details.append(f"LinkedIn: {contact.linkedin}")
    if contact.github:
        contact_details.append(f"GitHub: {contact.github}")
    if contact.website:
        contact_details.append(f"Website: {contact.website}")
    
    lines.append(" | ".join(contact_details))
    lines.append("")
    
    return "\n".join(lines)

def format_work_experience(experiences: List[WorkExperience]) -> str:
    """Format work experience section"""
    if not experiences:
        return ""
    
    lines = ["## Professional Experience", ""]
    
    for exp in experiences:
        # Job title and company
        title_line = f"**{exp.job_title}** | {exp.company}"
        if exp.location:
            title_line += f" | {exp.location}"
        lines.append(title_line)
        
        # Date range
        date_range = format_date_range(exp.start_date, exp.end_date, exp.is_current)
        lines.append(f"*{date_range}*")
        lines.append("")
        
        # Description
        if exp.description:
            lines.append(exp.description)
            lines.append("")
        
        # Achievements
        if exp.achievements:
            for achievement in exp.achievements:
                lines.append(f"• {achievement}")
            lines.append("")
    
    return "\n".join(lines)

def format_education(education_list: List[Education]) -> str:
    """Format education section"""
    if not education_list:
        return ""
    
    lines = ["## Education", ""]
    
    for edu in education_list:
        # Degree and field
        degree_line = f"**{edu.degree} in {edu.field_of_study}**"
        lines.append(degree_line)
        
        # Institution and location
        institution_line = edu.institution
        if edu.location:
            institution_line += f" | {edu.location}"
        lines.append(institution_line)
        
        # Additional details
        details = []
        if edu.graduation_date:
            details.append(f"Graduated: {edu.graduation_date.strftime('%B %Y')}")
        if edu.gpa:
            details.append(f"GPA: {edu.gpa}")
        if edu.honors:
            details.append(edu.honors)
        
        if details:
            lines.append(" | ".join(details))
        
        # Relevant coursework
        if edu.relevant_coursework:
            lines.append(f"Relevant Coursework: {', '.join(edu.relevant_coursework)}")
        
        lines.append("")
    
    return "\n".join(lines)

def format_skills(skills: List[SkillCategory]) -> str:
    """Format skills section"""
    if not skills:
        return ""
    
    lines = ["## Technical Skills", ""]
    
    for skill_cat in skills:
        lines.append(f"**{skill_cat.category}:** {', '.join(skill_cat.skills)}")
        lines.append("")
    
    return "\n".join(lines)

def format_projects(projects: List[Project]) -> str:
    """Format projects section"""
    if not projects:
        return ""
    
    lines = ["## Projects", ""]
    
    for project in projects:
        # Project name and URL
        project_line = f"**{project.name}**"
        if project.url:
            project_line += f" | [{project.url}]({project.url})"
        lines.append(project_line)
        
        # Date range
        if project.start_date:
            date_range = format_date_range(project.start_date, project.end_date)
            lines.append(f"*{date_range}*")
        
        # Technologies
        if project.technologies:
            lines.append(f"Technologies: {', '.join(project.technologies)}")
        
        lines.append("")
        
        # Description
        lines.append(project.description)
        lines.append("")
        
        # Achievements
        if project.achievements:
            for achievement in project.achievements:
                lines.append(f"• {achievement}")
            lines.append("")
    
    return "\n".join(lines)

def format_certifications(certifications: List[Certification]) -> str:
    """Format certifications section"""
    if not certifications:
        return ""
    
    lines = ["## Certifications", ""]
    
    for cert in certifications:
        cert_line = f"**{cert.name}** | {cert.issuing_organization}"
        
        if cert.issue_date:
            cert_line += f" | {cert.issue_date.strftime('%B %Y')}"
        
        if cert.expiration_date:
            cert_line += f" - {cert.expiration_date.strftime('%B %Y')}"
        
        lines.append(cert_line)
        
        if cert.credential_id:
            lines.append(f"Credential ID: {cert.credential_id}")
        
        if cert.url:
            lines.append(f"Verification: {cert.url}")
        
        lines.append("")
    
    return "\n".join(lines)

def convert_structured_resume_to_text(resume_data: StructuredResumeCreate) -> str:
    """Convert structured resume data to formatted markdown text"""
    sections = []
    
    # Contact Information
    sections.append(format_contact_info(resume_data.contact_info))
    
    # Professional Summary
    if resume_data.professional_summary:
        sections.append("## Professional Summary")
        sections.append("")
        sections.append(resume_data.professional_summary)
        sections.append("")
    
    # Work Experience
    work_exp = format_work_experience(resume_data.work_experience)
    if work_exp:
        sections.append(work_exp)
    
    # Education
    education = format_education(resume_data.education)
    if education:
        sections.append(education)
    
    # Skills
    skills = format_skills(resume_data.skills)
    if skills:
        sections.append(skills)
    
    # Projects
    projects = format_projects(resume_data.projects)
    if projects:
        sections.append(projects)
    
    # Certifications
    certifications = format_certifications(resume_data.certifications)
    if certifications:
        sections.append(certifications)
    
    # Languages
    if resume_data.languages:
        sections.append("## Languages")
        sections.append("")
        sections.append(", ".join(resume_data.languages))
        sections.append("")
    
    # Additional sections
    if resume_data.additional_sections:
        for section_name, section_content in resume_data.additional_sections.items():
            sections.append(f"## {section_name}")
            sections.append("")
            if isinstance(section_content, list):
                for item in section_content:
                    sections.append(f"• {item}")
            else:
                sections.append(str(section_content))
            sections.append("")
    
    return "\n".join(sections).strip()
