"""Add first name and last name to user table

Revision ID: b208afe6a411
Revises: a9eb942fe0f3
Create Date: 2025-06-08 15:41:49.990147

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'b208afe6a411'
down_revision: Union[str, None] = 'a9eb942fe0f3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('file_records', 'filename',
               existing_type=sa.VARCHAR(length=255),
               nullable=False)
    op.alter_column('file_records', 'content_type',
               existing_type=sa.VARCHAR(length=100),
               nullable=False)
    op.alter_column('file_records', 'size',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('file_records', 'content',
               existing_type=postgresql.BYTEA(),
               nullable=False)
    op.drop_index('ix_file_records_id', table_name='file_records')
    op.create_index(op.f('ix_file_records_id'), 'file_records', ['id'], unique=False)
    op.alter_column('resumes', 'file_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.add_column('users', sa.Column('first_name', sa.String(), nullable=True))
    op.add_column('users', sa.Column('last_name', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'last_name')
    op.drop_column('users', 'first_name')
    op.alter_column('resumes', 'file_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index(op.f('ix_file_records_id'), table_name='file_records')
    op.create_index('ix_file_records_id', 'file_records', ['id'], unique=True)
    op.alter_column('file_records', 'content',
               existing_type=postgresql.BYTEA(),
               nullable=True)
    op.alter_column('file_records', 'size',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('file_records', 'content_type',
               existing_type=sa.VARCHAR(length=100),
               nullable=True)
    op.alter_column('file_records', 'filename',
               existing_type=sa.VARCHAR(length=255),
               nullable=True)
    # ### end Alembic commands ###
