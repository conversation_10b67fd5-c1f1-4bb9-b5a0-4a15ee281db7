# job-application-backend\src\job_app\schemas\user.py

from pydantic import BaseModel, EmailStr, ConfigDict # Import ConfigDict for Pydantic v2+
from datetime import datetime

# Base schema for user, reusable
class UserBase(BaseModel):
    email: EmailStr # Pydantic validates this as an email format
    first_name: str | None = None
    last_name: str | None = None
    
    


# Schema for creating a user (used in POST requests)
class UserCreate(UserBase):
    password: str # Password is required for creation
    first_name: str
    last_name: str 

# Schema for returning user data (excluding password_hash)
class UserResponse(UserBase):
    id: int # Include the database ID
    email: EmailStr | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone: str | None = None
    location: str | None = None
    title: str | None = None
    company: str | None = None
    bio: str | None = None
    education: str | None = None
    experience: str | None = None
    skills: list[str] | None = None
    linkedIn: str | None = None
    github: str | None = None
    website: str | None = None
    avatar: str | None = None
    


    model_config = ConfigDict(from_attributes=True)


# Schemas for authentication (login endpoint response)
class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"

class UserUpdate(BaseModel):
    email: EmailStr | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone: str | None = None
    location: str | None = None
    title: str | None = None
    company: str | None = None
    bio: str | None = None
    education: str | None = None
    experience: str | None = None
    skills: list[str] | None = None
    linkedIn: str | None = None
    github: str | None = None
    website: str | None = None
    avatar: str | None = None
    
class UserUpdateResponse(UserUpdate):
    email: EmailStr | None = None
    first_name: str | None = None
    last_name: str | None = None
    phone: str | None = None
    location: str | None = None
    title: str | None = None
    company: str | None = None
    bio: str | None = None
    education: str | None = None
    experience: str | None = None
    skills: list[str] | None = None
    linkedIn: str | None = None
    github: str | None = None
    website: str | None = None
    avatar: str | None = None

    
