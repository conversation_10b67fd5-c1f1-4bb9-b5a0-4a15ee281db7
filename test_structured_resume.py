#!/usr/bin/env python3
"""
Test script for the structured resume endpoint
"""

import requests
import json
from datetime import date

# Test data for structured resume
test_resume_data = {
    "contact_info": {
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "phone": "+****************",
        "location": "San Francisco, CA",
        "linkedin": "https://linkedin.com/in/johndoe",
        "github": "https://github.com/johndoe",
        "website": "https://johndoe.dev"
    },
    "professional_summary": "Experienced software engineer with 5+ years of experience in full-stack development, specializing in Python, JavaScript, and cloud technologies. Proven track record of delivering scalable web applications and leading cross-functional teams.",
    "work_experience": [
        {
            "job_title": "Senior Software Engineer",
            "company": "Tech Corp",
            "location": "San Francisco, CA",
            "start_date": "2022-01-15",
            "end_date": None,
            "is_current": True,
            "description": "Leading development of microservices architecture for e-commerce platform",
            "achievements": [
                "Improved system performance by 40% through optimization of database queries and caching strategies",
                "Led a team of 4 developers in migrating legacy monolith to microservices architecture",
                "Implemented CI/CD pipelines reducing deployment time from 2 hours to 15 minutes",
                "Mentored junior developers and conducted code reviews"
            ]
        },
        {
            "job_title": "Software Engineer",
            "company": "StartupXYZ",
            "location": "San Francisco, CA",
            "start_date": "2020-06-01",
            "end_date": "2021-12-31",
            "is_current": False,
            "description": "Full-stack development for SaaS platform serving 10,000+ users",
            "achievements": [
                "Built RESTful APIs using Python Flask and PostgreSQL",
                "Developed responsive frontend using React and TypeScript",
                "Integrated third-party payment systems (Stripe, PayPal)",
                "Implemented automated testing achieving 90% code coverage"
            ]
        }
    ],
    "education": [
        {
            "degree": "Bachelor of Science",
            "field_of_study": "Computer Science",
            "institution": "University of California, Berkeley",
            "location": "Berkeley, CA",
            "graduation_date": "2020-05-15",
            "gpa": 3.8,
            "honors": "Magna Cum Laude",
            "relevant_coursework": [
                "Data Structures and Algorithms",
                "Database Systems",
                "Software Engineering",
                "Machine Learning"
            ]
        }
    ],
    "skills": [
        {
            "category": "Programming Languages",
            "skills": ["Python", "JavaScript", "TypeScript", "Java", "Go"]
        },
        {
            "category": "Frameworks & Libraries",
            "skills": ["React", "Node.js", "Flask", "Django", "Express.js"]
        },
        {
            "category": "Databases",
            "skills": ["PostgreSQL", "MongoDB", "Redis", "MySQL"]
        },
        {
            "category": "Cloud & DevOps",
            "skills": ["AWS", "Docker", "Kubernetes", "Jenkins", "Terraform"]
        }
    ],
    "projects": [
        {
            "name": "E-commerce Analytics Dashboard",
            "description": "Real-time analytics dashboard for e-commerce metrics with interactive visualizations",
            "technologies": ["React", "Python", "PostgreSQL", "D3.js"],
            "start_date": "2023-03-01",
            "end_date": "2023-06-30",
            "url": "https://github.com/johndoe/ecommerce-dashboard",
            "achievements": [
                "Processed over 1M data points daily with sub-second query response times",
                "Implemented real-time data streaming using WebSockets",
                "Created interactive charts and graphs for business insights"
            ]
        }
    ],
    "certifications": [
        {
            "name": "AWS Certified Solutions Architect",
            "issuing_organization": "Amazon Web Services",
            "issue_date": "2023-01-15",
            "expiration_date": "2026-01-15",
            "credential_id": "AWS-SAA-123456",
            "url": "https://aws.amazon.com/verification"
        }
    ],
    "languages": [
        "English (Native)",
        "Spanish (Conversational)",
        "French (Basic)"
    ],
    "additional_sections": {
        "Volunteer Work": [
            "Code mentor at local coding bootcamp (2022-Present)",
            "Volunteer web developer for non-profit organizations"
        ],
        "Publications": [
            "\"Microservices Best Practices\" - Tech Blog (2023)",
            "\"Scaling Web Applications\" - Conference Talk (2022)"
        ]
    }
}

def test_structured_resume_crud():
    """Test all structured resume CRUD operations"""

    # You'll need to replace this with your actual API base URL and auth token
    BASE_URL = "http://localhost:8000"  # Adjust as needed

    # You'll need to get an actual auth token by logging in first
    headers = {
        "Authorization": "Bearer YOUR_AUTH_TOKEN_HERE",
        "Content-Type": "application/json"
    }

    resume_id = None

    try:
        # 1. CREATE - Create structured resume
        print("1. Creating structured resume...")
        response = requests.post(
            f"{BASE_URL}/documents/resumes/structured",
            headers=headers,
            json=test_resume_data
        )

        if response.status_code == 201:
            resume_data = response.json()
            resume_id = resume_data["id"]
            print(f"✅ Resume created successfully! ID: {resume_id}")
        else:
            print(f"❌ Failed to create resume: {response.text}")
            return

        # 2. READ - Get resume details
        print(f"\n2. Getting resume details...")
        response = requests.get(
            f"{BASE_URL}/documents/resumes/{resume_id}",
            headers=headers
        )

        if response.status_code == 200:
            print("✅ Resume details retrieved successfully!")
        else:
            print(f"❌ Failed to get resume: {response.text}")

        # 3. READ - Get structured data
        print(f"\n3. Getting structured data...")
        response = requests.get(
            f"{BASE_URL}/documents/resumes/{resume_id}/structured-data",
            headers=headers
        )

        if response.status_code == 200:
            print("✅ Structured data retrieved successfully!")
        else:
            print(f"❌ Failed to get structured data: {response.text}")

        # 4. UPDATE - Update resume
        print(f"\n4. Updating resume...")
        update_data = {
            "professional_summary": "Updated: Senior software engineer with 6+ years of experience...",
            "work_experience": [
                {
                    "job_title": "Lead Software Engineer",
                    "company": "Updated Tech Corp",
                    "location": "San Francisco, CA",
                    "start_date": "2023-01-01",
                    "is_current": True,
                    "achievements": [
                        "Led team of 8 developers",
                        "Increased system performance by 60%"
                    ]
                }
            ]
        }

        response = requests.put(
            f"{BASE_URL}/documents/resumes/{resume_id}/structured",
            headers=headers,
            json=update_data
        )

        if response.status_code == 200:
            print("✅ Resume updated successfully!")
        else:
            print(f"❌ Failed to update resume: {response.text}")

        # 5. DOWNLOAD - Download PDF
        print(f"\n5. Downloading resume PDF...")
        response = requests.get(
            f"{BASE_URL}/documents/resumes/{resume_id}/download",
            headers=headers
        )

        if response.status_code == 200:
            with open(f"resume_{resume_id}.pdf", "wb") as f:
                f.write(response.content)
            print(f"✅ Resume PDF downloaded successfully! Saved as resume_{resume_id}.pdf")
        else:
            print(f"❌ Failed to download resume: {response.text}")

        # 6. DELETE - Delete resume (optional - uncomment if you want to test)
        # print(f"\n6. Deleting resume...")
        # response = requests.delete(
        #     f"{BASE_URL}/documents/resumes/{resume_id}",
        #     headers=headers
        # )
        #
        # if response.status_code == 204:
        #     print("✅ Resume deleted successfully!")
        # else:
        #     print(f"❌ Failed to delete resume: {response.text}")

    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")

if __name__ == "__main__":
    print("Testing structured resume CRUD operations...")
    print("Note: You need to update the BASE_URL and get a valid auth token first")
    print("Available operations:")
    print("- Create structured resume")
    print("- Get resume details")
    print("- Get structured data")
    print("- Update resume")
    print("- Download PDF")
    print("- Delete resume")
    print()
    # test_structured_resume_crud()  # Uncomment when ready to test
