# src/schemas/structured_resume.py

from pydantic import BaseModel, EmailStr, ConfigDict
from datetime import datetime, date
from typing import Optional, List

# --- Contact Information ---
class ContactInfo(BaseModel):
    """Contact information for the resume"""
    first_name: str
    last_name: str
    email: EmailStr
    phone: Optional[str] = None
    location: Optional[str] = None  # City, State or City, Country
    linkedin: Optional[str] = None
    github: Optional[str] = None
    website: Optional[str] = None

# --- Work Experience ---
class WorkExperience(BaseModel):
    """Individual work experience entry"""
    job_title: str
    company: str
    location: Optional[str] = None  # City, State
    start_date: date
    end_date: Optional[date] = None  # None means current position
    is_current: bool = False
    description: Optional[str] = None  # Brief company/role description
    achievements: List[str] = []  # List of bullet points/achievements
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Education ---
class Education(BaseModel):
    """Individual education entry"""
    degree: str  # e.g., "Bachelor of Science", "Master of Arts"
    field_of_study: str  # e.g., "Computer Science", "Business Administration"
    institution: str  # School/University name
    location: Optional[str] = None  # City, State
    graduation_date: Optional[date] = None
    gpa: Optional[float] = None
    honors: Optional[str] = None  # e.g., "Magna Cum Laude", "Dean's List"
    relevant_coursework: List[str] = []
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Skills ---
class SkillCategory(BaseModel):
    """Skills grouped by category"""
    category: str  # e.g., "Programming Languages", "Frameworks", "Tools"
    skills: List[str]

# --- Projects ---
class Project(BaseModel):
    """Individual project entry"""
    name: str
    description: str
    technologies: List[str] = []  # Technologies/tools used
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    url: Optional[str] = None  # GitHub, live demo, etc.
    achievements: List[str] = []  # Key accomplishments/features
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Certifications ---
class Certification(BaseModel):
    """Individual certification entry"""
    name: str
    issuing_organization: str
    issue_date: Optional[date] = None
    expiration_date: Optional[date] = None
    credential_id: Optional[str] = None
    url: Optional[str] = None  # Verification URL
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Main Structured Resume Schema ---
class StructuredResumeCreate(BaseModel):
    """Schema for creating a resume from structured data"""
    contact_info: ContactInfo
    professional_summary: Optional[str] = None  # Brief professional summary/objective
    work_experience: List[WorkExperience] = []
    education: List[Education] = []
    skills: List[SkillCategory] = []  # Grouped skills
    projects: List[Project] = []
    certifications: List[Certification] = []
    languages: List[str] = []  # e.g., ["English (Native)", "Spanish (Conversational)"]
    additional_sections: Optional[dict] = None  # For custom sections like "Volunteer Work", "Publications"

# --- Update Schema ---
class StructuredResumeUpdate(BaseModel):
    """Schema for updating a structured resume"""
    contact_info: Optional[ContactInfo] = None
    professional_summary: Optional[str] = None
    work_experience: Optional[List[WorkExperience]] = None
    education: Optional[List[Education]] = None
    skills: Optional[List[SkillCategory]] = None
    projects: Optional[List[Project]] = None
    certifications: Optional[List[Certification]] = None
    languages: Optional[List[str]] = None
    additional_sections: Optional[dict] = None

# --- List Item Schema ---
class StructuredResumeListItem(BaseModel):
    """Schema for structured resume list items with key information"""
    id: int
    upload_timestamp: datetime
    resume_type: str

    # Extract key info from structured data for easy identification
    full_name: Optional[str] = None
    email: Optional[str] = None
    current_position: Optional[str] = None
    current_company: Optional[str] = None

    # File info
    has_pdf: bool = False
    filename: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def from_resume(cls, resume):
        """Create list item from Resume model"""
        # Extract key info from structured data
        structured_data = resume.structured_data or {}
        contact_info = structured_data.get('contact_info', {})
        work_experience = structured_data.get('work_experience', [])

        # Get current position (first work experience that is current)
        current_position = None
        current_company = None
        for exp in work_experience:
            if exp.get('is_current', False):
                current_position = exp.get('job_title')
                current_company = exp.get('company')
                break

        # If no current position, get the most recent one
        if not current_position and work_experience:
            latest_exp = work_experience[0]
            current_position = latest_exp.get('job_title')
            current_company = latest_exp.get('company')

        return cls(
            id=resume.id,
            upload_timestamp=resume.upload_timestamp,
            resume_type=resume.resume_type,
            full_name=f"{contact_info.get('first_name', '')} {contact_info.get('last_name', '')}".strip(),
            email=contact_info.get('email'),
            current_position=current_position,
            current_company=current_company,
            has_pdf=resume.file is not None,
            filename=resume.file.filename if resume.file else None
        )

# --- Response Schema ---
class StructuredResumeResponse(BaseModel):
    """Response schema for structured resume creation"""
    id: int
    owner_id: int
    upload_timestamp: datetime
    extracted_text: Optional[str] = None
    structured_data: Optional[dict] = None  # The original structured data

    # File information
    file: Optional[dict] = None  # FileInfo when available

    model_config = ConfigDict(from_attributes=True)
