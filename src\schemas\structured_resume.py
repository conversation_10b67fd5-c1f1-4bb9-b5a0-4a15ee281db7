# src/schemas/structured_resume.py

from pydantic import BaseModel, EmailStr, ConfigDict
from datetime import datetime, date
from typing import Optional, List

# --- Contact Information ---
class ContactInfo(BaseModel):
    """Contact information for the resume"""
    first_name: str
    last_name: str
    email: EmailStr
    phone: Optional[str] = None
    location: Optional[str] = None  # City, State or City, Country
    linkedin: Optional[str] = None
    github: Optional[str] = None
    website: Optional[str] = None

# --- Work Experience ---
class WorkExperience(BaseModel):
    """Individual work experience entry"""
    job_title: str
    company: str
    location: Optional[str] = None  # City, State
    start_date: date
    end_date: Optional[date] = None  # None means current position
    is_current: bool = False
    description: Optional[str] = None  # Brief company/role description
    achievements: List[str] = []  # List of bullet points/achievements
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Education ---
class Education(BaseModel):
    """Individual education entry"""
    degree: str  # e.g., "Bachelor of Science", "Master of Arts"
    field_of_study: str  # e.g., "Computer Science", "Business Administration"
    institution: str  # School/University name
    location: Optional[str] = None  # City, State
    graduation_date: Optional[date] = None
    gpa: Optional[float] = None
    honors: Optional[str] = None  # e.g., "Magna Cum Laude", "Dean's List"
    relevant_coursework: List[str] = []
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Skills ---
class SkillCategory(BaseModel):
    """Skills grouped by category"""
    category: str  # e.g., "Programming Languages", "Frameworks", "Tools"
    skills: List[str]

# --- Projects ---
class Project(BaseModel):
    """Individual project entry"""
    name: str
    description: str
    technologies: List[str] = []  # Technologies/tools used
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    url: Optional[str] = None  # GitHub, live demo, etc.
    achievements: List[str] = []  # Key accomplishments/features
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Certifications ---
class Certification(BaseModel):
    """Individual certification entry"""
    name: str
    issuing_organization: str
    issue_date: Optional[date] = None
    expiration_date: Optional[date] = None
    credential_id: Optional[str] = None
    url: Optional[str] = None  # Verification URL
    
    model_config = ConfigDict(
        json_encoders={
            date: lambda v: v.isoformat()
        }
    )

# --- Main Structured Resume Schema ---
class StructuredResumeCreate(BaseModel):
    """Schema for creating a resume from structured data"""
    contact_info: ContactInfo
    professional_summary: Optional[str] = None  # Brief professional summary/objective
    work_experience: List[WorkExperience] = []
    education: List[Education] = []
    skills: List[SkillCategory] = []  # Grouped skills
    projects: List[Project] = []
    certifications: List[Certification] = []
    languages: List[str] = []  # e.g., ["English (Native)", "Spanish (Conversational)"]
    additional_sections: Optional[dict] = None  # For custom sections like "Volunteer Work", "Publications"

# --- Response Schema ---
class StructuredResumeResponse(BaseModel):
    """Response schema for structured resume creation"""
    id: int
    owner_id: int
    upload_timestamp: datetime
    extracted_text: Optional[str] = None
    structured_data: Optional[dict] = None  # The original structured data
    
    # File information
    file: Optional[dict] = None  # FileInfo when available
    
    model_config = ConfigDict(from_attributes=True)
