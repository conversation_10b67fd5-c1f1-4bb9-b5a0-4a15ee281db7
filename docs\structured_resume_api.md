# Structured Resume API Documentation

## Overview

The structured resume endpoint allows users to create professional resumes by submitting structured data (work experience, education, skills, etc.) instead of uploading a file. The system automatically formats the data into a professional resume and generates a PDF.

## Endpoint

```
POST /documents/resumes/structured
```

**Authentication Required:** Yes (Bearer token)

## Request Body Schema

The request body should contain structured resume data with the following fields:

### Contact Information (Required)
```json
{
  "contact_info": {
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>", 
    "email": "<EMAIL>",
    "phone": "+****************",
    "location": "San Francisco, CA",
    "linkedin": "https://linkedin.com/in/johndoe",
    "github": "https://github.com/johndoe",
    "website": "https://johndoe.dev"
  }
}
```

### Professional Summary (Optional)
```json
{
  "professional_summary": "Experienced software engineer with 5+ years..."
}
```

### Work Experience (Optional)
```json
{
  "work_experience": [
    {
      "job_title": "Senior Software Engineer",
      "company": "Tech Corp",
      "location": "San Francisco, CA",
      "start_date": "2022-01-15",
      "end_date": null,
      "is_current": true,
      "description": "Leading development of microservices architecture",
      "achievements": [
        "Improved system performance by 40%",
        "Led a team of 4 developers"
      ]
    }
  ]
}
```

### Education (Optional)
```json
{
  "education": [
    {
      "degree": "Bachelor of Science",
      "field_of_study": "Computer Science", 
      "institution": "University of California, Berkeley",
      "location": "Berkeley, CA",
      "graduation_date": "2020-05-15",
      "gpa": 3.8,
      "honors": "Magna Cum Laude",
      "relevant_coursework": ["Data Structures", "Algorithms"]
    }
  ]
}
```

### Skills (Optional)
```json
{
  "skills": [
    {
      "category": "Programming Languages",
      "skills": ["Python", "JavaScript", "TypeScript"]
    },
    {
      "category": "Frameworks",
      "skills": ["React", "Node.js", "Flask"]
    }
  ]
}
```

### Projects (Optional)
```json
{
  "projects": [
    {
      "name": "E-commerce Dashboard",
      "description": "Real-time analytics dashboard",
      "technologies": ["React", "Python", "PostgreSQL"],
      "start_date": "2023-03-01",
      "end_date": "2023-06-30",
      "url": "https://github.com/johndoe/project",
      "achievements": [
        "Processed 1M+ data points daily",
        "Sub-second query response times"
      ]
    }
  ]
}
```

### Certifications (Optional)
```json
{
  "certifications": [
    {
      "name": "AWS Certified Solutions Architect",
      "issuing_organization": "Amazon Web Services",
      "issue_date": "2023-01-15",
      "expiration_date": "2026-01-15",
      "credential_id": "AWS-SAA-123456",
      "url": "https://aws.amazon.com/verification"
    }
  ]
}
```

### Languages (Optional)
```json
{
  "languages": [
    "English (Native)",
    "Spanish (Conversational)"
  ]
}
```

### Additional Sections (Optional)
```json
{
  "additional_sections": {
    "Volunteer Work": [
      "Code mentor at local bootcamp",
      "Non-profit web developer"
    ],
    "Publications": [
      "Microservices Best Practices - Tech Blog (2023)"
    ]
  }
}
```

## Response

**Success (201 Created):**
```json
{
  "id": 123,
  "owner_id": 456,
  "upload_timestamp": "2023-08-23T18:30:00Z",
  "extracted_text": "# John Doe\n\<EMAIL> | +****************...",
  "resume_type": "structured",
  "structured_data": { /* original structured data */ },
  "file": {
    "id": 789,
    "filename": "structured_resume_456.pdf",
    "content_type": "application/pdf",
    "size": 245760
  }
}
```

**Error (400 Bad Request):**
```json
{
  "detail": "Validation error message"
}
```

**Error (500 Internal Server Error):**
```json
{
  "detail": "Failed to create structured resume: error details"
}
```

## Features

1. **Automatic Formatting**: Converts structured data into professional resume format
2. **PDF Generation**: Automatically generates a downloadable PDF
3. **Flexible Schema**: Support for various resume sections
4. **Data Preservation**: Original structured data is stored for future editing
5. **Professional Layout**: Clean, ATS-friendly resume format

## Usage Example

```bash
curl -X POST "http://localhost:8000/documents/resumes/structured" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "contact_info": {
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>"
    },
    "professional_summary": "Experienced developer...",
    "work_experience": [...],
    "education": [...],
    "skills": [...]
  }'
```

## Benefits

- **No file upload required**: Create resumes programmatically
- **Structured data**: Easy to parse and modify
- **Professional formatting**: Consistent, clean layout
- **PDF generation**: Ready-to-download resume
- **ATS-friendly**: Optimized for applicant tracking systems
