"""add user profile fields

Revision ID: ad867a6b38d9
Revises: b208afe6a411
Create Date: 2025-06-21 17:08:18.358246

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'ad867a6b38d9'
down_revision: Union[str, None] = 'b208afe6a411'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('phone', sa.String(), nullable=True))
    op.add_column('users', sa.Column('location', sa.String(), nullable=True))
    op.add_column('users', sa.Column('title', sa.String(), nullable=True))
    op.add_column('users', sa.Column('company', sa.String(), nullable=True))
    op.add_column('users', sa.Column('bio', sa.String(), nullable=True))
    op.add_column('users', sa.Column('education', sa.String(), nullable=True))
    op.add_column('users', sa.Column('experience', sa.String(), nullable=True))
    op.add_column('users', sa.Column('skills', sa.JSON(), nullable=True))
    op.add_column('users', sa.Column('linkedIn', sa.String(), nullable=True))
    op.add_column('users', sa.Column('github', sa.String(), nullable=True))
    op.add_column('users', sa.Column('website', sa.String(), nullable=True))
    op.add_column('users', sa.Column('avatar', sa.String(), nullable=True))
    op.drop_column('users', 'created_at')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.drop_column('users', 'avatar')
    op.drop_column('users', 'website')
    op.drop_column('users', 'github')
    op.drop_column('users', 'linkedIn')
    op.drop_column('users', 'skills')
    op.drop_column('users', 'experience')
    op.drop_column('users', 'education')
    op.drop_column('users', 'bio')
    op.drop_column('users', 'company')
    op.drop_column('users', 'title')
    op.drop_column('users', 'location')
    op.drop_column('users', 'phone')
    # ### end Alembic commands ###
